# 클라우드 → Proxmox 서버 이전 계획서

## 1. 개요

### 1.1 목적
- 클라우드 서버를 내부 Proxmox 클라우드로 이전하여 연간 운영비용 절감
- 웹서버 통합 및 크롤링 서버 폐지를 통한 인프라 최적화

### 1.2 이전 범위
- **현재**: 클라우드 기반 다중 서버 운영
- **목표**: Proxmox 기반 단일 웹서버 운영

## 2. 현재 인프라 현황

### 2.1 기존 클라우드 서버 구성 (NAVER CLOUD PLATFORM)
| 서버명 | 용도 | CPU | RAM | 스토리지 | 월 비용(VAT별도) |
|--------|------|-----|-----|----------|-----------------|
| ctis-crol | 크롤링 서버 | 2vCore | 2GB | 50GB HDD | ₩41,225 |
| ctis-nweb | 운영 웹서버 | 2vCore | 16GB | 50GB+300GB | ₩138,465 |
| ctis-db | 운영 DB서버 | 4vCore | 8GB | 50GB+2.8TB | ₩141,100 |
| dev-ctis-nweb | 개발 웹서버 | 2vCore | 16GB | 50GB+320GB | ₩138,465 |
| dev-ctis-db | 개발 DB서버 | 4vCore | 8GB | 50GB+300GB | ₩141,100 |
| smalt-web | PLANET 웹서버 | 4vCore | 16GB | 50GB+300GB | ₩181,645 |
| smalt-db | PLANET 크롤링서버 | 2vCore | 8GB | 50GB+50GB | ₩94,775 |
| smalt-crawl | 크롤링서버 | 2vCore | 4GB | 50GB | ₩66,300 |
| ctis-proxy | 프록시서버 | 2vCore | 16GB | 50GB | ₩138,465 |

### 2.2 추가 서비스 비용 (월간)
- Windows 2012 R2 라이선스: ₩320,000
- 오브젝트 스토리지: ₩284,544
- NAS (600GB x 2): ₩89,280
- 파일백업서비스: ₩30,000
- 서버이미지 백업: ₩205,230
- 스냅샷: ₩59,490
- 네트워크 트래픽: ₩19,340
- 로드밸런서: ₩70,000
- 공인 IP: ₩4,032
- IPS(침입차단): ₩250,000
- WAF(웹방화벽): ₩250,000
- Anti Virus: ₩216,000
- Managed 기술지원: ₩900,000

### 2.3 현재 총 비용
- **월간 합계 (VAT 별도)**: ₩3,484,000
- **월간 부가세**: ₩348,400
- **월간 합계 (VAT 포함)**: ₩3,832,400
- **연간 합계 (VAT 포함)**: **₩45,988,800**

*참고: 1년 약정 할인 적용 시 연간 ₩48,510,000 → ₩45,988,800*

## 3. 목표 Proxmox 인프라

### 3.1 Proxmox 클러스터 구성 (고가용성)
| 구성요소 | 사양 | 수량 | 단가 | 총 비용 |
|----------|------|------|------|--------|
| 메인 서버 | Intel Xeon Silver 4214R, 64GB RAM, 2TB NVMe SSD | 2대 | ₩8,500,000 | ₩17,000,000 |
| 스토리지 서버 | NAS 24Bay, 48TB 용량 | 1대 | ₩12,000,000 | ₩12,000,000 |
| 네트워크 스위치 | 48포트 기가비트 + 10G 업링크 | 2대 | ₩1,500,000 | ₩3,000,000 |
| 방화벽 | Enterprise 급 방화벽 | 1대 | ₩3,000,000 | ₩3,000,000 |
| UPS | 3000VA 이중화 | 2대 | ₩800,000 | ₩1,600,000 |
| 랙/케이블링 | 42U 랙, 케이블링 | 1식 | ₩1,000,000 | ₩1,000,000 |
| **초기 투자 비용** | | | | **₩37,600,000** |

### 3.2 통합 가상머신 구성
| VM명 | 용도 | CPU | RAM | 스토리지 | 비고 |
|------|------|-----|-----|----------|------|
| web-prod | 운영 웹서버 통합 | 8vCore | 32GB | 500GB SSD | ctis-nweb + smalt-web 통합 |
| db-prod | 운영 DB서버 통합 | 8vCore | 32GB | 4TB SSD | ctis-db + smalt-db 통합 |
| web-dev | 개발 웹서버 | 4vCore | 16GB | 200GB SSD | 개발 환경 |
| db-dev | 개발 DB서버 | 4vCore | 16GB | 1TB SSD | 개발 환경 |
| proxy | 프록시/로드밸런서 | 4vCore | 8GB | 100GB SSD | nginx + HAProxy |
| monitor | 모니터링 | 2vCore | 8GB | 200GB SSD | Zabbix + ELK Stack |
| backup | 백업 서버 | 2vCore | 8GB | 10TB HDD | 백업 전용 |

**크롤링 서버 폐지**: 크롤링 기능은 운영 웹서버에 통합하여 별도 서버 불필요

## 4. 연간 절감 비용 분석

### 4.1 현재 클라우드 비용 상세 분석
- **연간 클라우드 비용**: ₩45,988,800 (VAT 포함, 1년 약정 할인 적용)
- **월간 평균**: ₩3,832,400
- **할인 전 정가**: ₩48,510,000 (연간)

### 4.2 Proxmox 연간 운영비
| 항목 | 월간 비용 | 연간 비용 | 비고 |
|------|----------|----------|------|
| 전력비 (10kW 평균) | ₩1,200,000 | ₩14,400,000 | 24시간 운영 기준 |
| 인터넷 회선 (전용선 100Mbps) | ₩800,000 | ₩9,600,000 | 고정 IP 포함 |
| 데이터센터 코로케이션 | ₩500,000 | ₩6,000,000 | 랙 임대, 냉각, 보안 |
| 유지보수 및 부품교체 | ₩300,000 | ₩3,600,000 | 하드웨어 보증 만료 후 |
| 백업 스토리지 (오프사이트) | ₩200,000 | ₩2,400,000 | 클라우드 백업 서비스 |
| 모니터링 및 관리 도구 | ₩100,000 | ₩1,200,000 | 상용 솔루션 라이선스 |
| **총 운영비** | **₩3,100,000** | **₩37,200,000** |

### 4.3 비용 비교 및 절감액
| 구분 | 클라우드 | Proxmox | 절감액 |
|------|----------|---------|--------|
| **연간 운영비** | ₩45,988,800 | ₩37,200,000 | **₩8,788,800** |
| **월간 절감액** | | | **₩732,400** |

### 4.4 투자 회수 기간
- **초기 투자**: ₩37,600,000
- **연간 절감**: ₩8,788,800
- **회수 기간**: 4.3년

### 4.5 5년 장기 전망 (클라우드 비용 연 5% 인상 가정)
| 연도 | 클라우드 비용 | Proxmox 운영비 | 연간 절감 | 누적 절감 |
|------|--------------|---------------|----------|----------|
| 1년차 | ₩45,988,800 | ₩37,200,000 | ₩8,788,800 | ₩8,788,800 |
| 2년차 | ₩48,288,240 | ₩39,060,000 | ₩9,228,240 | ₩18,017,040 |
| 3년차 | ₩50,702,652 | ₩41,013,000 | ₩9,689,652 | ₩27,706,692 |
| 4년차 | ₩53,237,785 | ₩43,063,650 | ₩10,174,135 | ₩37,880,827 |
| 5년차 | ₩55,899,674 | ₩45,216,833 | ₩10,682,841 | ₩48,563,668 |

**5년 총 순이익**: ₩48,563,668 - ₩37,600,000 = **₩10,963,668**

### 4.6 추가 절감 요소
- **라이선스 비용 절감**: Windows Server 라이선스 ₩320,000/월 → Linux 무료
- **관리 서비스 비용 절감**: Managed 서비스 ₩900,000/월 → 내부 관리
- **확장성**: 추가 VM 생성 시 클라우드 대비 무료
- **데이터 전송비**: 무제한 내부 트래픽
- **보안 솔루션**: 오픈소스 활용으로 라이선스 비용 절감

## 5. 이전 계획

### 5.1 Phase 1: 준비 단계 (1-2주)
- [ ] Proxmox 서버 구매 및 설치
- [ ] 네트워크 환경 구성
- [ ] 백업 시스템 구축
- [ ] 테스트 환경 구성

### 5.2 Phase 2: 마이그레이션 (1주)
- [ ] 웹서버 데이터 백업
- [ ] Proxmox VM 생성 및 구성
- [ ] 애플리케이션 이전
- [ ] 데이터베이스 마이그레이션
- [ ] DNS 설정 변경

### 5.3 Phase 3: 검증 및 최적화 (1주)
- [ ] 서비스 정상 동작 확인
- [ ] 성능 테스트
- [ ] 모니터링 시스템 구축
- [ ] 백업 자동화 설정

### 5.4 Phase 4: 완료 (1주)
- [ ] 클라우드 서버 종료
- [ ] 크롤링 서버 기능 제거
- [ ] 문서화 완료
- [ ] 운영팀 교육

## 6. 위험 요소 및 대응 방안

### 6.1 기술적 위험
| 위험 요소 | 영향도 | 대응 방안 |
|----------|--------|----------|
| 하드웨어 장애 | 높음 | RAID 구성, 예비 부품 확보 |
| 네트워크 장애 | 중간 | 이중화 회선, 백업 연결 |
| 데이터 손실 | 높음 | 일일 백업, 오프사이트 백업 |

### 6.2 운영적 위험
| 위험 요소 | 영향도 | 대응 방안 |
|----------|--------|----------|
| 서비스 중단 | 높음 | 단계적 이전, 롤백 계획 |
| 성능 저하 | 중간 | 사전 성능 테스트, 모니터링 |
| 운영 복잡성 | 중간 | 운영 매뉴얼 작성, 교육 |

## 7. 성공 지표

### 7.1 기술적 지표
- 서비스 가용성: 99.9% 이상
- 응답 시간: 기존 대비 동일 수준 유지
- 데이터 무손실 이전

### 7.2 비즈니스 지표
- 연간 ₩8,788,800 비용 절감 달성
- 4.3년 내 투자 회수
- 5년 후 총 ₩10,963,668 순이익 창출
- 운영 효율성 향상

## 8. 결론

본 이전 계획을 통해 **연간 약 879만원의 비용을 절감**하고, 4.3년 후부터는 순이익을 창출할 수 있습니다.

**주요 효과:**
- 현재 연간 클라우드 비용: ₩45,988,800
- 이전 후 연간 운영비: ₩37,200,000
- **연간 절감액: ₩8,788,800 (19% 절감)**
- 5년 총 순이익: **₩10,963,668**

웹서버 통합과 크롤링 서버 폐지를 통해 인프라를 단순화하고, 내부 관리 역량을 강화하여 장기적으로 안정적인 비용 절감 효과를 달성할 수 있습니다.

## 9. 상세 기술 구현 계획

### 9.1 Proxmox 설치 및 구성
```bash
# Proxmox VE 설치 후 기본 구성
pveversion
pveum user add admin@pve
pveum passwd admin@pve
```

### 9.2 네트워크 구성
- **외부 IP**: 고정 IP 1개 할당
- **내부 네트워크**: ***********/24
- **VLAN 구성**:
  - VLAN 10: 웹서버 (************/24)
  - VLAN 20: 관리 (************/24)

### 9.3 스토리지 구성
- **로컬 스토리지**: NVMe SSD (VM 디스크)
- **백업 스토리지**: 외장 HDD (백업 전용)
- **스냅샷**: 일일 자동 스냅샷

### 9.4 보안 설정
- 방화벽 규칙 설정
- SSH 키 기반 인증
- 정기 보안 업데이트
- 침입 탐지 시스템 (Fail2ban)

## 10. 운영 매뉴얼

### 10.1 일상 운영 작업
- **일일**: 시스템 상태 확인, 로그 검토
- **주간**: 백업 검증, 성능 모니터링
- **월간**: 보안 패치, 용량 계획

### 10.2 장애 대응 절차
1. 장애 감지 및 알림
2. 초기 진단 및 영향도 평가
3. 임시 조치 및 서비스 복구
4. 근본 원인 분석 및 재발 방지

### 10.3 백업 및 복구 절차
- **백업 주기**: 일일 증분, 주간 전체
- **보관 기간**: 30일 (로컬), 90일 (오프사이트)
- **복구 테스트**: 월 1회

## 11. 예상 일정

| 주차 | 작업 내용 | 담당자 | 완료 기준 |
|------|----------|--------|----------|
| 1주차 | 하드웨어 구매 및 설치 | 인프라팀 | Proxmox 설치 완료 |
| 2주차 | 네트워크 및 보안 설정 | 네트워크팀 | 테스트 환경 구축 |
| 3주차 | 데이터 마이그레이션 | 개발팀 | 서비스 이전 완료 |
| 4주차 | 검증 및 최적화 | 전체팀 | 운영 환경 안정화 |

## 12. 부록

### 12.1 하드웨어 사양 상세
```
메인 서버 (2대): Dell PowerEdge R750 또는 동급
- CPU: Intel Xeon Silver 4214R (12코어, 24스레드, 2.4GHz)
- RAM: 64GB DDR4 ECC (128GB까지 확장 가능)
- 스토리지: 2TB NVMe SSD (RAID 1) + 4TB SATA HDD (RAID 1)
- 네트워크: 4x 1GbE + 2x 10GbE 포트
- 전력: 750W 최대

스토리지 서버: Synology DS3622xs+ 또는 동급
- CPU: Intel Xeon D-1531 (6코어, 12스레드)
- RAM: 32GB DDR4 ECC
- 베이: 24Bay (3.5" SATA/SAS)
- 스토리지: 48TB (2TB x 24, RAID 6)
- 네트워크: 4x 1GbE + 2x 10GbE 포트
```

### 12.2 소프트웨어 라이선스
- Proxmox VE: 무료 (Community Edition)
- 백업 솔루션: Proxmox Backup Server (무료)
- 모니터링: Zabbix (오픈소스)
- 방화벽: pfSense (오픈소스)

### 12.3 연락처 및 지원
- **기술 지원**: 내부 IT팀
- **하드웨어 지원**: 벤더 3년 보증
- **응급 연락처**: [24시간 대기 번호]

### 12.4 관련 문서
- Proxmox 설치 가이드
- 네트워크 구성 매뉴얼
- 백업 및 복구 절차서
- 장애 대응 매뉴얼

---
**문서 버전**: v1.0
**작성일**: 2024년 12월
**작성자**: 시스템 관리팀
**검토자**: 인프라 팀장
**승인자**: IT 부서장
**다음 검토일**: 2025년 6월
