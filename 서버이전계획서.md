클라우드 서버 내부 Proxmox 클라우드 이전 검토(안)


<2024. 12. 경영관리팀>

□ 개요

 ❍ (보고목적) 연간 클라우드 서비스 이용료 절감을 위한 내부 Proxmox 클라우드 구축 및 이전 검토
   - 현재 NAVER CLOUD PLATFORM 연간 이용료: 45,988,800원 (VAT 포함)
   - 내부 Proxmox 클라우드 구축을 통한 대폭적인 비용 절감 및 운영 효율성 향상 기대
      ☞ 클라우드 의존도 탈피 및 자체 인프라 운영 역량 강화 필요

  ❍ (관련) 데이터전략센터 서버 운영비 절감 방안 검토 요청

  ❍ (경과) 현재 NAVER CLOUD PLATFORM을 통해 9개 VM 운영 중
   - 운영서버: ctis-crol, ctis-nweb, ctis-db, smalt-web, smalt-db, smalt-crawl, ctis-proxy
   - 개발서버: dev-ctis-nweb, dev-ctis-db
   - 추가 서비스: Windows 라이선스, 백업, 보안, 관리 서비스 등 포함

□ 검토 주요내용(안)

  ❍ (1안: 기 구축된 내부 Proxmox 인프라 활용) 아래와 같은 조건으로 추진

 ▶ 현재 내부에 구축된 Proxmox 서버 인프라를 활용하여 즉시 이전 가능
 ▶ 대폭적인 서버 통합 및 최적화
    - 현재 9개 VM → 3개 VM으로 통합 (웹서버 1개, DB서버 1개, 프록시서버 1개)
    - 크롤링 서버 완전 폐지 (크롤링 기능 사용 중단)
    - 개발서버는 운영서버 내 개발환경으로 통합
 ▶ 서브디렉토리 기반 멀티사이트 구성
    - https://www.ctis.re.kr/ (메인 사이트)
    - https://www.ctis.re.kr/dataarchive/ (데이터 아카이브)
    - https://www.ctis.re.kr/planet/ (PLANET 사이트)
    - https://www.ctis.re.kr/map/ (전략지도)
    - https://www.ctis.re.kr/dev/ (개발 환경)
    - 프록시서버를 통한 요청 라우팅 및 로드밸런싱
 ▶ 추가 투자비용: 0원 (기존 인프라 활용)
    - 연간 운영비: 기존 Proxmox 운영비에 포함 (추가 비용 없음)
    - 연간 절감액: 45,988,800원 (100% 절감)

  ❍ (2안: 기존 전산실 인프라 활용 서버 증설)
    - 기존 전산실 인프라(네트워크, 전력, 냉각 등) 활용
    - 고성능 서버 1대 추가 구매하여 기존 Proxmox 클러스터에 노드 추가
    - 초기 투자비용: 22,000,000원 (고성능 서버 1대)
    - 연간 운영비: 기존 전산실 운영비에 포함 (추가 비용 최소)
    - 연간 절감액: 45,988,800원 (100% 절감)

<장점 및 단점 비교>

| 구분 | 장점 | 단점 |
|------|------|------|
| **1안<br>(기존 Proxmox 활용)** | **• 최대 비용 절감 효과**<br>- 연간 45,988,800원 완전 절감 (100% 절감)<br>- 추가 투자비용 0원<br>- 즉시 효과 발생<br><br>**• 신속한 이전 및 운영**<br>- 기존 인프라 활용으로 즉시 이전 가능<br>- 검증된 내부 시스템 활용<br>- 이전 작업 단순화<br><br>**• 효율적인 멀티사이트 운영**<br>- 9개 VM → 3개 VM 대폭 통합<br>- 서브디렉토리 기반 사이트 통합 관리<br>- 프록시를 통한 안정적 라우팅 | **• 기존 시스템 의존성**<br>- 현재 Proxmox 시스템 안정성에 의존<br>- 기존 시스템 용량 제약 가능성<br><br>**• 프록시 서버 복잡성**<br>- 리버스 프록시 설정 및 관리 필요<br>- URL 라우팅 규칙 구성 복잡<br>- 프록시 장애 시 전체 서비스 영향<br><br>**• 크롤링 기능 상실**<br>- 크롤링 서버 폐지로 관련 기능 중단<br>- 향후 크롤링 필요시 재구축 필요 |
| **2안<br>(전산실 서버 증설)** | **• 최적의 성능 및 안정성**<br>- 고성능 전용 서버로 최고 성능 확보<br>- 기존 Proxmox 클러스터 확장으로 고가용성<br>- 기존 인프라 활용으로 비용 효율적<br><br>**• 완전한 비용 절감**<br>- 연간 45,988,800원 완전 절감 (100% 절감)<br>- 5.7개월 투자 회수 후 순이익 창출 | **• 초기 투자비용 발생**<br>- 22,000,000원 서버 구매비 필요<br>- 5.7개월 투자 회수 기간<br><br>**• 하드웨어 관리 부담**<br>- 서버 하드웨어 직접 관리 필요<br>- 장애 시 자체 대응 필요 |
| **3안<br>(현상유지)** | **• 안정적인 서비스 연속성**<br>- 현재 운영 중인 서비스 무중단<br>- 검증된 클라우드 플랫폼 활용<br><br>**• 운영 부담 최소화**<br>- 하드웨어 관리 불필요<br>- 24시간 기술지원 제공 | **• 지속적인 고비용 부담**<br>- 연간 45,988,800원 지속 지출<br>- 클라우드 비용 지속 상승 우려<br><br>**• 클라우드 의존도 심화**<br>- 서비스 제공사 정책 변경 위험<br>- 데이터 주권 제약 |

□ 실무자 의견
 ❍ 1안(기 구축된 내부 Proxmox 인프라 활용)으로 즉시 추진하는 것이 최적
   - 추가 투자비용 없이 연간 4,599만원 완전 절감으로 즉시 효과 발생
   - 기존 검증된 내부 Proxmox 인프라 활용으로 안정성 확보
   - 대폭적인 서버 통합(9개→3개)으로 관리 효율성 극대화
   - 크롤링 서버 폐지로 불필요한 리소스 제거 및 보안 위험 감소

 ❍ 2안(전산실 서버 증설) 추가 검토 의견
   - 기존 전산실 인프라(네트워크, 전력, 냉각) 활용으로 추가 운영비 최소화
   - 고성능 서버 구매비(2,200만원)만 소요되어 1안 대비 성능과 안정성 확보 가능
   - 기존 Proxmox 클러스터에 노드 추가로 고가용성 및 확장성 확보
   - **5.7개월 투자 회수** 후 1안과 동일한 100% 비용 절감 효과
   - 예산 여유 시 2안 검토 권장 (장기적 안정성 및 성능 우수)

□ 현재 클라우드 서비스 현황

<NAVER CLOUD PLATFORM 서버 구성 및 비용>
(단위: 원, VAT 포함)

| 서버명 | 용도 | CPU | RAM | 스토리지 | 월 비용 |
|--------|------|-----|-----|----------|---------|
| ctis-crol | 크롤링 서버 | 2vCore | 2GB | 50GB HDD | 41,225 |
| ctis-nweb | 운영 웹서버 | 2vCore | 16GB | 50GB+300GB | 138,465 |
| ctis-db | 운영 DB서버 | 4vCore | 8GB | 50GB+2.8TB | 141,100 |
| dev-ctis-nweb | 개발 웹서버 | 2vCore | 16GB | 50GB+320GB | 138,465 |
| dev-ctis-db | 개발 DB서버 | 4vCore | 8GB | 50GB+300GB | 141,100 |
| smalt-web | PLANET 웹서버 | 4vCore | 16GB | 50GB+300GB | 181,645 |
| smalt-db | PLANET 크롤링서버 | 2vCore | 8GB | 50GB+50GB | 94,775 |
| smalt-crawl | 크롤링서버 | 2vCore | 4GB | 50GB | 66,300 |
| ctis-proxy | 프록시서버 | 2vCore | 16GB | 50GB | 138,465 |

<추가 서비스 비용>

| 구분 | 세부내용 | 월 비용 | 비고 |
|------|----------|---------|------|
| 라이선스 | Windows 2012 R2 | 320,000 | 서버 라이선스 |
| 스토리지 | 오브젝트 스토리지 | 284,544 | 백업 및 파일 저장 |
| 백업 | NAS (600GB x 2) | 89,280 | 이중화 백업 |
| 백업 | 파일백업서비스 | 30,000 | 자동 백업 |
| 백업 | 서버이미지 백업 | 205,230 | 시스템 백업 |
| 백업 | 스냅샷 | 59,490 | 증분 백업 |
| 네트워크 | 트래픽 비용 | 19,340 | 아웃바운드 트래픽 |
| 네트워크 | 로드밸런서 | 70,000 | 부하분산 |
| 네트워크 | 공인 IP | 4,032 | 고정 IP |
| 보안 | IPS(침입차단) | 250,000 | 보안 솔루션 |
| 보안 | WAF(웹방화벽) | 250,000 | 웹 보안 |
| 보안 | Anti Virus | 216,000 | 백신 솔루션 |
| 관리 | Managed 기술지원 | 900,000 | 24시간 기술지원 |
| **합계** | | **3,832,400** | **월간 총 비용** |

**연간 총 비용: 45,988,800원** (VAT 포함, 1년 약정 할인 적용)





붙임 : 1. Proxmox 이전 절차 및 일정(안), 2. 서버 통합 및 비용 절감 효과 분석

붙임1

 Proxmox 인프라 활용 이전 절차 및 일정(안)

<1안: 기존 Proxmox 활용 시 추진 절차 및 일정>

| 단계 | 작업 내용 | 기간 | 담당 부서 | 완료 기준 |
|------|----------|------|----------|----------|
| **1단계** | • 기존 Proxmox 시스템 점검<br>• 용량 및 성능 검토<br>• 필요시 리소스 증설 | 2025.1월 | 정보화팀<br>데이터전략센터 | 시스템 준비 완료 |
| **2단계** | • 통합 VM 설계 및 생성<br>• 프록시, 웹서버, DB서버 VM 생성 | 2025.2월 | 데이터전략센터<br>정보화팀 | VM 생성 및 기본 설정 완료 |
| **3단계** | • 애플리케이션 설치 및 구성<br>• 테스트 데이터 이전<br>• 기능 검증 | 2025.3월 | 데이터전략센터 | 테스트 환경 정상 동작 |
| **4단계** | • 실제 데이터 마이그레이션<br>• DNS 설정 변경<br>• 서비스 전환 | 2025.4월 | 데이터전략센터<br>정보화팀 | 서비스 이전 완료 |
| **5단계** | • 크롤링 서버 완전 폐지<br>• 클라우드 서비스 종료<br>• 문서화 및 교육 | 2025.5월 | 전체 부서 | 이전 작업 완료 |

<2안: 전산실 서버 증설 시 추진 절차 및 일정>

| 단계 | 작업 내용 | 기간 | 담당 부서 | 완료 기준 |
|------|----------|------|----------|----------|
| **1단계** | • HPE ProLiant DL380 Gen10 Plus 구매 및 도입<br>• 전산실 설치 및 네트워크 연결<br>• Proxmox 노드 추가 구성 | 2025.1~2월 | 정보화팀<br>경영관리팀 | 고성능 클러스터 확장 완료 |
| **2단계** | • 고가용성 VM 설계 및 생성<br>• 이중화 구성 및 테스트 | 2025.3월 | 정보화팀<br>데이터전략센터 | HA 환경 구축 완료 |
| **3단계** | • 애플리케이션 설치 및 구성<br>• 테스트 데이터 이전<br>• 성능 및 안정성 검증 | 2025.4월 | 데이터전략센터 | 테스트 환경 정상 동작 |
| **4단계** | • 실제 데이터 마이그레이션<br>• DNS 설정 변경<br>• 서비스 전환 | 2025.5월 | 데이터전략센터<br>정보화팀 | 서비스 이전 완료 |
| **5단계** | • 클라우드 서비스 종료<br>• 시스템 최적화<br>• 문서화 및 교육 | 2025.6월 | 전체 부서 | 이전 작업 완료 |

붙임2

 서버 통합 및 비용 절감 효과 세부 분석

<기존 Proxmox 활용 시 서버 통합 계획>

| 현재 클라우드 VM | 통합 후 Proxmox VM | 비고 |
|-----------------|-------------------|------|
| ctis-nweb (운영 웹서버) | **web-server** | 모든 웹서비스 통합 |
| smalt-web (PLANET 웹서버) | ↗ (통합) | 서브디렉토리로 통합 |
| dev-ctis-nweb (개발 웹서버) | ↗ (통합) | 개발환경도 통합 |
| ctis-db (운영 DB서버) | **db-server** | 모든 DB 통합 |
| smalt-db (PLANET DB서버) | ↗ (통합) | 단일 DB서버로 통합 |
| dev-ctis-db (개발 DB서버) | ↗ (통합) | 개발 DB도 통합 |
| ctis-proxy (프록시서버) | **proxy-server** | 리버스 프록시 전용 |
| ctis-crol (크롤링서버) | **폐지** | 크롤링 기능 중단 |
| smalt-crawl (크롤링서버) | **폐지** | 크롤링 기능 중단 |

**통합 결과: 9개 VM → 3개 VM (67% 감소)**

<서브디렉토리 기반 사이트 구성>

| 기존 사이트 | 통합 후 URL | 비고 |
|------------|-------------|------|
| ctis.re.kr | https://www.ctis.re.kr/ | 메인 사이트 |
| planet 사이트 | https://www.ctis.re.kr/planet/ | 서브디렉토리 |
| 개발 환경 | https://www.ctis.re.kr/dev/ | 개발/테스트 |

<통합 VM 사양 계획>

| VM명 | 용도 | CPU | RAM | 스토리지 | 비고 |
|------|------|-----|-----|----------|------|
| **proxy-server** | 리버스 프록시 | 2vCore | 4GB | 100GB SSD | Nginx, SSL 인증서 관리 |
| **web-server** | 통합 웹서버 | 8vCore | 32GB | 1TB SSD | 모든 웹서비스 통합 |
| **db-server** | 통합 DB서버 | 8vCore | 32GB | 4TB SSD | 모든 DB 통합 |

<연간 비용 절감 효과>
(단위: 원, VAT 포함)

| 구분 | 클라우드 비용 | 기존 Proxmox 활용 | 절감액 | 절감률 |
|------|--------------|-------------------|--------|--------|
| **VM 운영비** | 13,481,760 | 0 | 13,481,760 | 100% |
| **라이선스** | 3,840,000 | 0 | 3,840,000 | 100% |
| **스토리지/백업** | 8,208,480 | 0 | 8,208,480 | 100% |
| **네트워크** | 1,124,160 | 0 | 1,124,160 | 100% |
| **보안** | 8,544,000 | 0 | 8,544,000 | 100% |
| **관리/지원** | 10,800,000 | 0 | 10,800,000 | 100% |
| **합계** | **45,988,800** | **0** | **45,988,800** | **100%** |

<즉시 효과 및 장기 전망>

| 항목 | 효과 |
|------|------|
| **즉시 효과** | 연간 45,988,800원 완전 절감 |
| **추가 투자** | 0원 (기존 인프라 활용) |
| **회수 기간** | 즉시 (투자비용 없음) |
| **5년 총 절감** | 229,944,000원 (클라우드 비용 5% 연간 인상 가정) |

**프록시 서버 구성의 핵심 기능:**
- **리버스 프록시**: 클라이언트 요청을 백엔드 웹서버로 라우팅
- **로드밸런싱**: 웹서버 부하 분산 (향후 확장 시)
- **SSL 터미네이션**: HTTPS 인증서 통합 관리
- **URL 라우팅**: 서브디렉토리 기반 사이트 분기
- **캐싱**: 정적 콘텐츠 캐싱으로 성능 향상
- **보안**: WAF(웹 애플리케이션 방화벽) 기능 통합 가능

**Nginx 프록시 설정 예시:**
```nginx
server {
    listen 443 ssl;
    server_name www.ctis.re.kr;

    # 메인 사이트
    location / {
        proxy_pass http://web-server:8080/ctis/;
    }

    # PLANET 사이트
    location /planet/ {
        proxy_pass http://web-server:8080/planet/;
    }

    # 개발 환경
    location /dev/ {
        proxy_pass http://web-server:8080/dev/;
    }
}
```

<2안: 전산실 서버 증설 시 비용 분석>

<서버 구매 비용 및 사양 세부내역>
(단위: 원, VAT 포함)

| 구분 | 세부내용 | 수량 | 단가 | 총액 | 비고 |
|------|----------|------|------|------|------|
| **서버** | HPE ProLiant DL380 Gen10 Plus | 1대 | 22,000,000 | 22,000,000 | 기존 클러스터 노드 추가 |
| **합계** | | | | **22,000,000** | 기존 인프라 활용 |

<HPE ProLiant DL380 Gen10 Plus 상세 사양>

| 구성요소 | 사양 | 비고 |
|----------|------|------|
| **CPU** | Intel Xeon Gold 5218R (2.1GHz, 20코어, 40스레드) x 2개 | 총 40코어, 80스레드 |
| **메모리** | 128GB DDR4-2933 ECC RDIMM | 확장 가능 (최대 3TB) |
| **스토리지** | • 480GB SSD x 2 (OS용, RAID 1)<br>• 2TB NVMe SSD x 4 (데이터용, RAID 10) | 고성능 스토리지 |
| **네트워크** | • 1GbE x 4포트<br>• 10GbE x 2포트 | 기존 네트워크 연결 |
| **전원** | 800W Platinum 이중화 전원 | 고효율, 무정전 |
| **관리** | iLO 5 Advanced | 원격 관리 |
| **폼팩터** | 2U 랙마운트 | 기존 랙 설치 |
| **보증** | 3년 NBD 하드웨어 지원 | 무상 A/S |

**기존 전산실 인프라 활용 항목 (추가 비용 없음):**
- 네트워크 스위치 및 케이블링 (기존 활용)
- UPS 및 전력 공급 (기존 용량 내)
- 랙 공간 및 냉각 시설 (기존 활용)
- 방화벽 및 보안 시스템 (기존 활용)

<2안 투자 회수 분석>

| 항목 | 금액 | 비고 |
|------|------|------|
| **초기 투자** | 22,000,000원 | 고성능 서버 구매비만 |
| **연간 절감** | 45,988,800원 | 100% 클라우드 비용 절감 |
| **회수 기간** | 5.7개월 | 22,000,000 ÷ 45,988,800 = 0.48년 |
| **1년 후 순이익** | 23,988,800원 | 5.7개월 회수 후 순이익 |
| **5년 총 순이익** | 207,944,000원 | 클라우드 비용 5% 연간 인상 가정 |

**1안 vs 2안 비교:**

| 구분 | 1안 (기존 활용) | 2안 (서버 증설) | 차이점 |
|------|----------------|----------------|--------|
| **초기 투자** | 0원 | 22,000,000원 | 2,200만원 차이 |
| **회수 기간** | 즉시 | 1.4년 | 1.4년 지연 |
| **성능** | 기존 시스템 의존 | 전용 고성능 서버 | 성능 우위 |
| **안정성** | 기존 시스템 의존 | 고가용성 클러스터 | 안정성 우위 |
| **확장성** | 제한적 | 우수 | 확장성 우위 |

**기존 Proxmox 인프라 활용의 핵심 장점:**
- 추가 투자비용 없이 즉시 100% 비용 절감 효과
- 검증된 내부 시스템 활용으로 안정성 확보
- 프록시를 통한 효율적인 멀티사이트 통합 관리

<2안 고성능 VM 구성 계획>

| VM명 | 용도 | CPU | RAM | 스토리지 | 비고 |
|------|------|-----|-----|----------|------|
| **proxy-server** | 리버스 프록시 | 4vCore | 8GB | 200GB SSD | Nginx, 고성능 처리 |
| **web-server** | 통합 웹서버 | 16vCore | 64GB | 2TB NVMe | 모든 웹서비스 통합 |
| **db-server** | 통합 DB서버 | 16vCore | 64GB | 4TB NVMe | 모든 DB 통합, 고성능 |
| **backup-server** | 백업 서버 | 4vCore | 16GB | 8TB SSD | 백업 및 복구 |

**고성능 서버 활용 효과:**
- **CPU 성능**: 40코어/80스레드로 대용량 처리 가능
- **메모리**: 128GB로 대용량 데이터 처리 및 캐싱
- **스토리지**: NVMe SSD로 초고속 I/O 성능
- **확장성**: 필요시 추가 VM 생성 여유 충분

**전산실 서버 증설의 핵심 장점:**
- 기존 인프라 활용으로 최소 투자비용 (서버비만)
- 전용 고성능 서버로 최고 성능 및 안정성 확보
- Proxmox 클러스터 확장으로 고가용성 구현
- 향후 5년 이상 안정적 운영 가능한 충분한 성능

---
**문서 버전**: v2.0
**작성일**: 2024년 12월
**작성자**: 경영관리팀
**검토자**: 데이터전략센터장
**승인자**: 경영지원부장
**다음 검토일**: 2025년 3월
