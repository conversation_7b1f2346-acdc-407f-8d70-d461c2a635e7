# 클라우드 → Proxmox 서버 이전 계획서

## 1. 개요

### 1.1 목적
- 클라우드 서버를 내부 Proxmox 클라우드로 이전하여 연간 운영비용 절감
- 웹서버 통합 및 크롤링 서버 폐지를 통한 인프라 최적화

### 1.2 이전 범위
- **현재**: 클라우드 기반 다중 서버 운영
- **목표**: Proxmox 기반 단일 웹서버 운영

## 2. 현재 인프라 현황

### 2.1 기존 클라우드 서버 구성
| 서버 유형 | 수량 | 사양 | 월 비용 | 연간 비용 |
|----------|------|------|---------|----------|
| 웹서버 | 2대 | 2vCPU, 4GB RAM, 50GB SSD | ₩80,000 | ₩960,000 |
| 크롤링서버 | 1대 | 1vCPU, 2GB RAM, 20GB SSD | ₩40,000 | ₩480,000 |
| 로드밸런서 | 1대 | 기본 | ₩30,000 | ₩360,000 |
| 네트워크/트래픽 | - | 월 평균 | ₩50,000 | ₩600,000 |
| **총 비용** | | | **₩200,000** | **₩2,400,000** |

### 2.2 추가 클라우드 서비스 비용
- 백업 스토리지: 월 ₩20,000 (연간 ₩240,000)
- 모니터링 서비스: 월 ₩15,000 (연간 ₩180,000)
- SSL 인증서: 연간 ₩100,000
- **총 추가 비용**: 연간 ₩520,000

**현재 총 연간 비용: ₩2,920,000**

## 3. 목표 Proxmox 인프라

### 3.1 Proxmox 서버 구성
| 구성요소 | 사양 | 예상 비용 |
|----------|------|----------|
| 물리 서버 | Intel Xeon E-2236, 32GB RAM, 1TB NVMe SSD | ₩3,500,000 |
| 네트워크 장비 | 기가비트 스위치, 방화벽 | ₩800,000 |
| UPS | 1500VA | ₩300,000 |
| 랙/케이블링 | 기본 구성 | ₩200,000 |
| **초기 투자 비용** | | **₩4,800,000** |

### 3.2 가상머신 구성
- **통합 웹서버**: 4vCPU, 8GB RAM, 100GB SSD
- **백업 VM**: 2vCPU, 4GB RAM, 500GB HDD
- **모니터링 VM**: 1vCPU, 2GB RAM, 50GB SSD

## 4. 연간 절감 비용 분석

### 4.1 비용 비교
| 항목 | 클라우드 (연간) | Proxmox (연간) | 절감액 |
|------|----------------|----------------|--------|
| 서버 운영비 | ₩2,400,000 | ₩0 | ₩2,400,000 |
| 추가 서비스 | ₩520,000 | ₩0 | ₩520,000 |
| 전력비 | ₩0 | ₩360,000 | -₩360,000 |
| 인터넷 회선 | ₩0 | ₩480,000 | -₩480,000 |
| 유지보수 | ₩0 | ₩200,000 | -₩200,000 |
| **순 절감액** | | | **₩1,880,000** |

### 4.2 투자 회수 기간
- 초기 투자: ₩4,800,000
- 연간 절감: ₩1,880,000
- **회수 기간: 2.6년**

### 4.3 3년 총 절감 효과
- 3년 총 절감: ₩5,640,000
- 초기 투자 차감: ₩5,640,000 - ₩4,800,000 = **₩840,000 순이익**

### 4.4 5년 장기 전망
| 연도 | 클라우드 비용 | Proxmox 운영비 | 연간 절감 | 누적 절감 |
|------|--------------|---------------|----------|----------|
| 1년차 | ₩2,920,000 | ₩1,040,000 | ₩1,880,000 | ₩1,880,000 |
| 2년차 | ₩3,066,000 | ₩1,092,000 | ₩1,974,000 | ₩3,854,000 |
| 3년차 | ₩3,219,000 | ₩1,147,000 | ₩2,072,000 | ₩5,926,000 |
| 4년차 | ₩3,380,000 | ₩1,204,000 | ₩2,176,000 | ₩8,102,000 |
| 5년차 | ₩3,549,000 | ₩1,264,000 | ₩2,285,000 | ₩10,387,000 |

**5년 총 순이익: ₩5,587,000** (초기 투자 ₩4,800,000 차감 후)

### 4.5 추가 절감 요소
- **인력 비용 절감**: 클라우드 관리 업무 감소로 월 20시간 절약
- **확장성**: 필요시 추가 VM 생성 비용 없음
- **데이터 전송비**: 내부 네트워크 사용으로 트래픽 비용 제거

## 5. 이전 계획

### 5.1 Phase 1: 준비 단계 (1-2주)
- [ ] Proxmox 서버 구매 및 설치
- [ ] 네트워크 환경 구성
- [ ] 백업 시스템 구축
- [ ] 테스트 환경 구성

### 5.2 Phase 2: 마이그레이션 (1주)
- [ ] 웹서버 데이터 백업
- [ ] Proxmox VM 생성 및 구성
- [ ] 애플리케이션 이전
- [ ] 데이터베이스 마이그레이션
- [ ] DNS 설정 변경

### 5.3 Phase 3: 검증 및 최적화 (1주)
- [ ] 서비스 정상 동작 확인
- [ ] 성능 테스트
- [ ] 모니터링 시스템 구축
- [ ] 백업 자동화 설정

### 5.4 Phase 4: 완료 (1주)
- [ ] 클라우드 서버 종료
- [ ] 크롤링 서버 기능 제거
- [ ] 문서화 완료
- [ ] 운영팀 교육

## 6. 위험 요소 및 대응 방안

### 6.1 기술적 위험
| 위험 요소 | 영향도 | 대응 방안 |
|----------|--------|----------|
| 하드웨어 장애 | 높음 | RAID 구성, 예비 부품 확보 |
| 네트워크 장애 | 중간 | 이중화 회선, 백업 연결 |
| 데이터 손실 | 높음 | 일일 백업, 오프사이트 백업 |

### 6.2 운영적 위험
| 위험 요소 | 영향도 | 대응 방안 |
|----------|--------|----------|
| 서비스 중단 | 높음 | 단계적 이전, 롤백 계획 |
| 성능 저하 | 중간 | 사전 성능 테스트, 모니터링 |
| 운영 복잡성 | 중간 | 운영 매뉴얼 작성, 교육 |

## 7. 성공 지표

### 7.1 기술적 지표
- 서비스 가용성: 99.9% 이상
- 응답 시간: 기존 대비 동일 수준 유지
- 데이터 무손실 이전

### 7.2 비즈니스 지표
- 연간 ₩1,880,000 비용 절감 달성
- 2.6년 내 투자 회수
- 운영 효율성 향상

## 8. 결론

본 이전 계획을 통해 연간 약 188만원의 비용을 절감하고, 2.6년 후부터는 순이익을 창출할 수 있습니다. 웹서버 통합과 크롤링 서버 폐지를 통해 인프라를 단순화하고 관리 효율성을 높일 수 있습니다.

## 9. 상세 기술 구현 계획

### 9.1 Proxmox 설치 및 구성
```bash
# Proxmox VE 설치 후 기본 구성
pveversion
pveum user add admin@pve
pveum passwd admin@pve
```

### 9.2 네트워크 구성
- **외부 IP**: 고정 IP 1개 할당
- **내부 네트워크**: ***********/24
- **VLAN 구성**:
  - VLAN 10: 웹서버 (************/24)
  - VLAN 20: 관리 (************/24)

### 9.3 스토리지 구성
- **로컬 스토리지**: NVMe SSD (VM 디스크)
- **백업 스토리지**: 외장 HDD (백업 전용)
- **스냅샷**: 일일 자동 스냅샷

### 9.4 보안 설정
- 방화벽 규칙 설정
- SSH 키 기반 인증
- 정기 보안 업데이트
- 침입 탐지 시스템 (Fail2ban)

## 10. 운영 매뉴얼

### 10.1 일상 운영 작업
- **일일**: 시스템 상태 확인, 로그 검토
- **주간**: 백업 검증, 성능 모니터링
- **월간**: 보안 패치, 용량 계획

### 10.2 장애 대응 절차
1. 장애 감지 및 알림
2. 초기 진단 및 영향도 평가
3. 임시 조치 및 서비스 복구
4. 근본 원인 분석 및 재발 방지

### 10.3 백업 및 복구 절차
- **백업 주기**: 일일 증분, 주간 전체
- **보관 기간**: 30일 (로컬), 90일 (오프사이트)
- **복구 테스트**: 월 1회

## 11. 예상 일정

| 주차 | 작업 내용 | 담당자 | 완료 기준 |
|------|----------|--------|----------|
| 1주차 | 하드웨어 구매 및 설치 | 인프라팀 | Proxmox 설치 완료 |
| 2주차 | 네트워크 및 보안 설정 | 네트워크팀 | 테스트 환경 구축 |
| 3주차 | 데이터 마이그레이션 | 개발팀 | 서비스 이전 완료 |
| 4주차 | 검증 및 최적화 | 전체팀 | 운영 환경 안정화 |

## 12. 부록

### 12.1 하드웨어 사양 상세
```
서버: Dell PowerEdge R340 또는 동급
- CPU: Intel Xeon E-2236 (6코어, 12스레드, 3.4GHz)
- RAM: 32GB DDR4 ECC (확장 가능)
- 스토리지: 1TB NVMe SSD + 2TB SATA HDD
- 네트워크: 2x 1GbE 포트
- 전력: 350W 최대
```

### 12.2 소프트웨어 라이선스
- Proxmox VE: 무료 (Community Edition)
- 백업 솔루션: Proxmox Backup Server (무료)
- 모니터링: Zabbix (오픈소스)
- 방화벽: pfSense (오픈소스)

### 12.3 연락처 및 지원
- **기술 지원**: 내부 IT팀
- **하드웨어 지원**: 벤더 3년 보증
- **응급 연락처**: [24시간 대기 번호]

### 12.4 관련 문서
- Proxmox 설치 가이드
- 네트워크 구성 매뉴얼
- 백업 및 복구 절차서
- 장애 대응 매뉴얼

---
**문서 버전**: v1.0
**작성일**: 2024년 12월
**작성자**: 시스템 관리팀
**검토자**: 인프라 팀장
**승인자**: IT 부서장
**다음 검토일**: 2025년 6월
